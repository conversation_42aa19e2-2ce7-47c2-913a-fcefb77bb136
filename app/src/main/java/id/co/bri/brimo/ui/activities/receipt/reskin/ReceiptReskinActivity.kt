package id.co.bri.brimo.ui.activities.receipt.reskin

import android.animation.ValueAnimator
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityReceiptReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.image.ImageHelper
import id.co.bri.brimo.domain.helpers.reskin.ImageHelperReskin
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.domain.helpers.reskin.mappingReceipt
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.util.copyToClipboard
import java.io.File

class ReceiptReskinActivity: NewSkinBaseActivity() {
    private var _binding: ActivityReceiptReskinBinding? = null
    protected val binding get() = _binding!!

    private var isExpanded = false
    private var isEmptyMoreContent = false

    private lateinit var imageHelper: ImageHelper

    companion object {
        var dataReceipt: ReceiptRevampResponse?= null
        private const val ANIMATE_DUR: Long = 300
        private var TYPE = ReceiptType.OTHER

        private var dataAccount: AccountModel?= null

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, data: ReceiptRevampResponse,
                         sumberDana: AccountModel?, receiptType: ReceiptType = ReceiptType.OTHER) {
            TYPE = receiptType
            isFromFastMenu = fromFastMenu
            dataReceipt = data
            dataAccount = sumberDana

            caller.apply {
                startActivityForResult(Intent(
                    this,
                    ReceiptReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }

        // Expand animation
        private fun expand(view: View) {
            view.measure(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            val targetHeight = view.measuredHeight

            view.layoutParams.height = 0
            view.visibility = View.VISIBLE

            val animator = ValueAnimator.ofInt(0, targetHeight).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    view.layoutParams.height = animation.animatedValue as Int
                    view.requestLayout()
                }
            }
            animator.start()
        }

        // Collapse animation
        private fun collapse(view: View) {
            val initialHeight = view.measuredHeight

            val animator = ValueAnimator.ofInt(initialHeight, 0).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    val value = animation.animatedValue as Int
                    view.layoutParams.height = value
                    view.requestLayout()
                    if (value == 0) {
                        view.visibility = View.GONE
                    }
                }
            }
            animator.start()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityReceiptReskinBinding.inflate(layoutInflater)

        imageHelper = ImageHelper(this)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Tidak ngapa-ngapain
            }
        })
    }

    private fun onBindView() {
        val fadeInNormal = AnimationUtils.loadAnimation(this, R.anim.fade_in_normal)

        Handler(Looper.getMainLooper()).postDelayed({
            showAnimateCard(AnimationUtils.loadAnimation(this, R.anim.fade_in_from_bottom))
        }, 300)
        Handler(Looper.getMainLooper()).postDelayed({
            showAnimateButton(AnimationUtils.loadAnimation(this@ReceiptReskinActivity, R.anim.fade_in_from_bottom))
        }, 600)
        showAnimateIcon(fadeInNormal)

        val onProcessTrans = dataReceipt?.isOnProcess ?: false

        binding.ivIconTransaction.setImageResource(if(onProcessTrans) R.drawable.ic_timer_process_reskin else R.drawable.ic_checklist_receipt_newskin)

        binding.tvTitleTransaction.text = dataReceipt?.title

        binding.ivCopyClipboard.setOnClickListener {
            copyToClipboard(text = binding.tvNoToken.text.toString())
        }
        binding.btnDownload.setOnClickListener {
            startDownload {

            }
        }

        binding.btnShare.setOnClickListener {
            startDownload(true) { file ->
                shareBitmap(file)
            }
        }

        dataReceipt?.sourceAccountDataView?.let {
            GeneralHelper.loadIconTransaction(
                this@ReceiptReskinActivity,
                it.iconPath,
                it.iconName,
                binding.ivAccount,
                R.drawable.ic_bri)
        }

        dataReceipt?.mappingReceipt(TYPE)?.apply {
            binding.tvNoToken.text = token

            binding.tvTransactionNote.text = note

            binding.tvNameAccount.text = detailTransaction[0].name
            binding.tvContentAccount.text = detailTransaction[0].content

            GeneralHelper.loadIconTransaction(
                this@ReceiptReskinActivity,
                detailTransaction[1].iconUrl,
                detailTransaction[1].iconUrl.split("\\.".toRegex())
                    .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
                binding.ivTransaction,
                GeneralHelper.getImageId(this@ReceiptReskinActivity, "bri")
            )

            binding.tvNameTransaction.text = detailTransaction[1].name
            binding.tvTransactionContent.text = detailTransaction[1].content

            binding.tvNoPref.text = prefNumber
            binding.tvTransactionType.text = transactionType

            binding.tvAdminFee.text = adminFee
            binding.tvNominalCard.text = nominalCount

            binding.tvDateTime.text = dateTime
            binding.tvNominal.text = nominalPayment

            isEmptyMoreContent = contentList.size > 0

            if(contentList.size>0) {
                contentList.forEach { content ->
                    val inflater = LayoutInflater.from(this@ReceiptReskinActivity)
                    val itemView = inflater.inflate(R.layout.item_bill_detail, binding.llMoreContent, false)

                    val tvNameContent = itemView.findViewById<TextView>(R.id.tv_name_content)
                    val tvContent = itemView.findViewById<TextView>(R.id.tv_content)

                    tvNameContent.apply {
                        text = content.title
                        setTextColor(Color.parseColor("#7B90A6"))
                    }
                    tvContent.apply {
                        text = content.content
                        setTextColor(Color.parseColor("#181C21"))
                    }

                    binding.llMoreContent.addView(itemView)
                }
            }

            binding.llToken.visibility = if(token.isNullOrEmpty()) View.GONE else View.VISIBLE
        }

        binding.btnLihatLebih.setOnClickListener {
            isExpanded = !isExpanded
            if (isExpanded) {
                expand(binding.llShowHide)
                binding.vLineBillDetail.visibility = View.VISIBLE
                if(TYPE == ReceiptType.EWALLET) expand(binding.llNote)
                if(isEmptyMoreContent) {
                    binding.llMainMoreContent.visibility = View.VISIBLE
                    expand(binding.llMoreContent)
                }

                binding.btnLihatLebih.apply {
                    text = GeneralHelper.getString(R.string.sembunyikan_with_space)
                    setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        ContextCompat.getDrawable(context, R.drawable.ic_chevron_up_reskin),
                        null
                    )
                }

            } else {
                collapse(binding.llShowHide)
                binding.vLineBillDetail.visibility = View.GONE
                if(TYPE == ReceiptType.EWALLET) collapse(binding.llNote)
                if(isEmptyMoreContent) {
                    binding.llMainMoreContent.visibility = View.GONE
                    collapse(binding.llMoreContent)
                }

                binding.btnLihatLebih.apply{
                    text = GeneralHelper.getString(R.string.lihat_detail_with_space)
                    setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        null,
                        ContextCompat.getDrawable(context, R.drawable.ic_chevron_down_reskin),
                        null
                    )
                }
            }
        }

        binding.btnSubmit.setOnClickListener {
            startActivity(Intent(this, if(!isFromFastMenu) DashboardIBActivity::class.java
            else FastMenuNewSkinActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            })
        }
    }

    private fun showAnimateCard(animation: Animation) {
        binding.scallopCard.startAnimation(animation)
        binding.scallopCard.visibility = View.VISIBLE
    }

    private fun showAnimateButton(animation: Animation) {
        binding.clBottom.startAnimation(animation)
        binding.clBottom.visibility = View.VISIBLE
    }

    private fun showAnimateIcon(animation: Animation) {
        binding.ivLogo.startAnimation(animation)
        binding.ivLogo.visibility = View.VISIBLE
    }

    private fun injectDependency() {
    }

    private fun onBindIntentData() {
    }

    private fun startDownload(isShare: Boolean = false, res: (File) -> Unit) {
        binding.myContent.post {
            val originalBackground = binding.myContent.background
            binding.myContent.setBackgroundResource(R.drawable.bg_receipt_basic_ns)
            ImageHelperReskin.apply {
                val bitmap = captureFullView(binding.myContent)
                binding.myContent.background = originalBackground
                try {
                    saveBitmap(bitmap) { file ->
                        res.invoke(file)
                    }

                    if(!isShare)
//                        showSnackbar("Berhasil Download", ALERT_CONFIRM)
                        GeneralHelperNewSkin.showSnackBar(binding.myContent,"Berhasil Download", ALERT_CONFIRM)
                } catch (e: Exception) {
                    if(!isShare)
//                        showSnackbar("Gagal Download", ALERT_ERROR)
                        GeneralHelperNewSkin.showSnackBar(binding.myContent,"Gagal Download", ALERT_ERROR)
                }
            }
        }
    }

    override fun onBackPressed() {

    }

    fun shareBitmap(file: File) {
        val uri = FileProvider.getUriForFile(
            this,
            "${packageName}.fileprovider",
            file
        )
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "image/png"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        startActivity(Intent.createChooser(intent, "Share your image"))
    }
}